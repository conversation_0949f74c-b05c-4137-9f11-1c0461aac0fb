import * as THREE from "three";
import { deleteModel } from "../../utils/sceneUtils/DeleteModel.js";
import gsap from "gsap";

import { lineSegmentsToCurve } from "../../utils/sceneUtils/ToCurve";

export class CreatePointFlyLine {
  constructor(sceneManage) {
    if (CreatePointFlyLine.instance || !sceneManage) return CreatePointFlyLine.instance;
    CreatePointFlyLine.instance = this;

    this.sceneManage = sceneManage;

    this.LineToColor = {
      blue: new THREE.Color("#085591").convertLinearToSRGB(),
      red: new THREE.Color("#FF0000").convertLinearToSRGB(),
      green: new THREE.Color("#008000").convertLinearToSRGB(),
      orange: new THREE.Color("#F2C037").convertLinearToSRGB(),
    };
    this.pointFlyLineObj = {};
  }
  addPointFlyLine(model, parentScene) {
    if (this.pointFlyLineObj[parentScene.name] == null) this.pointFlyLineObj[parentScene.name] = {};
    model.traverse((line) => {
      if (!line.geometry || line.name.includes("None")) return;
      // const lineUuid = line.uuid
      const lineUuid = line.name;
      if (line.name.includes("气管")) return this.sceneManage.pipeAirflow.addPipeAirflow(line, parentScene);
      const curve = lineSegmentsToCurve(line);
      const segment = Math.ceil(curve.getLength()) * 50;

      const stepPts = curve.getSpacedPoints(segment);
      const lineGeo = new THREE.BufferGeometry().setFromPoints(stepPts);
      const length = stepPts.length;
      var percents = new Float32Array(length);
      for (let i = 0; i < length; i += 1) {
        percents[i] = i / length;
      }
      lineGeo.setAttribute("percent", new THREE.BufferAttribute(percents, 1));

      let color = 0xeeee00;
      if (lineUuid.includes("blue")) color = this.LineToColor["blue"];
      if (lineUuid.includes("red")) color = this.LineToColor["red"];
      if (lineUuid.includes("green")) color = this.LineToColor["green"];
      if (lineUuid.includes("orange")) color = this.LineToColor["orange"];

      let flowMat = this.getPointFlyLineMaterial(segment, color);
      const point = new THREE.Points(lineGeo, flowMat);
      point.scale.copy(line.scale);

      this.pointFlyLineObj[parentScene.name][lineUuid] = {
        point,
        isPlay: true,
      };

      point.onBeforeRender = () => {
        if (!this.pointFlyLineObj[parentScene.name][lineUuid].isPlay) return;
        flowMat.uniforms.u_time.value += 0.0005;
      };

      // parentScene.attach(point)
      // this.materialAni(point)
    });
  }
  getPointFlyLineMaterial(segment, color = 0xeeee00) {
    return new THREE.ShaderMaterial({
      uniforms: {
        u_time: { value: 2.0 },
        number: { value: Math.round(segment / 400) || 1 },
        speed: { value: 50.0 / Math.round(segment / 50) || 1 },
        length0: { value: 0.4 },
        size: { value: 130 },
        color: { value: new THREE.Color(color) },
      },
      vertexShader: `
  
          #include <common>
          #include <logdepthbuf_pars_vertex>
          varying vec2 vUv;
          attribute float percent;
          uniform float u_time;
          uniform float number;
          uniform float speed;
          uniform float length0;
  
  
          varying float opacity;
          uniform float size;
              void main() {
                  vUv = uv;
                  vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );
                  float l = clamp(1.0-length0,0.0,1.0);
                  float dis = length(mvPosition.xyz - cameraPosition);
                  gl_PointSize = clamp(fract(percent*number + l - u_time*number*speed)-l ,0.0,1.) * size * (1./length0)*(1./-mvPosition.z);
                  opacity = gl_PointSize;
                  gl_Position = projectionMatrix * mvPosition;
                  #include <logdepthbuf_vertex>
                  }
     `,
      fragmentShader: `
          #ifdef GL_ES
          #include <common>
        #include <logdepthbuf_pars_fragment>
          precision mediump float;
          #endif
              varying float opacity;
              uniform vec3 color;
              void main(){
                #include <logdepthbuf_fragment>
                
                  if(opacity <=0.1 || distance(gl_PointCoord, vec2(0.5)) > 0.5){
                      discard;
                  }
              gl_FragColor = vec4(color,1.0);
              }
         `,
    });
  }

  changePointFlyLineStatus(isPlay, sceneName) {
    //有场景名称则对具体场景内的物体进行操作
    if (sceneName != null) {
      for (const j in this.pointFlyLineObj[sceneName]) {
        this.pointFlyLineObj[sceneName][j].isPlay = isPlay;
      }
      return;
    }
    //没有指定场景名称则对所有场景内的物体进行操作
    for (const i in this.pointFlyLineObj) {
      for (const j in this.pointFlyLineObj[i]) {
        this.pointFlyLineObj[i][j].isPlay = isPlay;
      }
    }
  }

  // materialAni(point) {
  //   console.log(point.material);
  //   point.onBeforeRender = (renderer, scene, camera, geometry, material, group) => {

  //     // if (point.material.isStop) return
  //     // point.material.uniforms.u_time.value += 0.0005;
  //   };
  // }
  destroy() {
    CreatePointFlyLine.instance = null;
  }
}
