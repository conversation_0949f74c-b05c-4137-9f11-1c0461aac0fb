import * as THREE from "three";
import { EARTH_FRAGMENTS, EARTH_RADIUS } from "../../../../constants";
import { GUI } from "lil-gui";
import CustomShaderMaterial from "three-custom-shader-material/vanilla";

// Halo shader definition
const haloShader = {
  vertexShader: `
  varying vec3 vVertexWorldPosition;
  varying vec3 vVertexNormal;

  void main() {

    vVertexNormal = normalize(normalMatrix * normal);

    vVertexWorldPosition = (modelMatrix * vec4(position, 1.0)).xyz;

    // set gl_Position
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
  `,
  fragmentShader: `
  uniform vec3 glowColor;
  uniform float coeficient;
  uniform float power;
  uniform float uOpacity;

  varying vec3 vVertexNormal;
  varying vec3 vVertexWorldPosition;

  void main() {
    vec3 worldCameraToVertex = vVertexWorldPosition - cameraPosition;
    vec3 viewCameraToVertex = (viewMatrix * vec4(worldCameraToVertex, 0.0)).xyz;
    viewCameraToVertex = normalize(viewCameraToVertex);
    float intensity =
        pow(coeficient + dot(vVertexNormal, viewCameraToVertex), power);

    // Use CustomShaderMaterial's csm_DiffuseColor instead of gl_FragColor
   float finalAlpha =  clamp(intensity * uOpacity, 0.0, 1.0);
    csm_DiffuseColor = vec4(glowColor, finalAlpha);
  }
  `,
};

class Halo {
  constructor(scene, { color = "#7da4ff" } = {}) {
    this.scene = scene;
    this.color = color;
    this.innerMesh = null;
    this.outerMesh = null;
    this.innerGeometry = null;
    this.outerGeometry = null;
    this.innerMaterial = null;
    this.outerMaterial = null;
    this.innerGroup = null;
    this.outerGroup = null;

    // GUI properties
    this.gui = null;
    this.folder = null;

    // Outer material control parameters
    this.outerMaterialParams = {
      visible: true,
      coeficient: 0.04,
      power: 3.5,
      color: "#62cffe",
      opacity: 1,
      scale: 1.1,
      geometryRadius: 1.02,
      geometryDetail: EARTH_FRAGMENTS,
      side: "BackSide", // "FrontSide", "BackSide", "DoubleSide"
      depthWrite: false,
      depthTest: true,
      blending: "NormalBlending", // "NormalBlending", "AdditiveBlending", "SubtractiveBlending", "MultiplyBlending"
    };

    // Inner material control parameters (for comparison)
    this.innerMaterialParams = {
      visible: true,
      coeficient: 0.9,
      power: 10,
      color: "#62cffe",
      opacity: 0.5,
      scale: 1.0001,
      geometryRadius: 1.01,
      depthWrite: true,
    };

    // Color options for GUI
    this.colorOptions = {
      蓝色: "#7da4ff",
      青色: "#6be1ff",
      绿色: "#00ff88",
      紫色: "#8800ff",
      红色: "#ff0088",
      橙色: "#ff8800",
      白色: "#ffffff",
      黄色: "#ffff00",
    };

    // Blending mode options
    this.blendingOptions = {
      正常: "NormalBlending",
      叠加: "AdditiveBlending",
      减法: "SubtractiveBlending",
      乘法: "MultiplyBlending",
    };

    // Side options
    this.sideOptions = {
      正面: "FrontSide",
      背面: "BackSide",
      双面: "DoubleSide",
    };

    this.init();
  }

  init() {
    this.createGeometries();
    this.createMaterials();
    this.createMeshes();
    this.addToScene();
    // this.initGUI();
  }

  createGeometries() {
    this.innerGeometry = new THREE.IcosahedronGeometry(EARTH_RADIUS * this.innerMaterialParams.geometryRadius, this.innerMaterialParams.geometryDetail || EARTH_FRAGMENTS);

    this.outerGeometry = new THREE.IcosahedronGeometry(EARTH_RADIUS * this.outerMaterialParams.geometryRadius, this.outerMaterialParams.geometryDetail);
  }

  createMaterials() {
    // Inner halo material
    this.innerMaterial = new CustomShaderMaterial({
      baseMaterial: THREE.MeshBasicMaterial,
      vertexShader: haloShader.vertexShader,
      fragmentShader: haloShader.fragmentShader,
      uniforms: {
        coeficient: { value: this.innerMaterialParams.coeficient },
        power: { value: this.innerMaterialParams.power },
        glowColor: { value: new THREE.Color(this.innerMaterialParams.color) },
        uOpacity: { value: this.innerMaterialParams.opacity },
      },
      transparent: true,
      depthWrite: this.innerMaterialParams.depthWrite,
    });

    // Outer halo material
    this.outerMaterial = new CustomShaderMaterial({
      baseMaterial: THREE.MeshBasicMaterial,
      vertexShader: haloShader.vertexShader,
      fragmentShader: haloShader.fragmentShader,
      uniforms: {
        coeficient: { value: this.outerMaterialParams.coeficient },
        power: { value: this.outerMaterialParams.power },
        glowColor: { value: new THREE.Color(this.outerMaterialParams.color) },
        uOpacity: { value: this.outerMaterialParams.opacity },
      },
      side: this.getSideConstant(this.outerMaterialParams.side),
      transparent: true,
      depthWrite: this.outerMaterialParams.depthWrite,
      depthTest: this.outerMaterialParams.depthTest,
      blending: this.getBlendingConstant(this.outerMaterialParams.blending),
    });
  }

  createMeshes() {
    // Inner mesh
    this.innerMesh = new THREE.Mesh(this.innerGeometry, this.innerMaterial);
    this.innerGroup = new THREE.Group();
    this.innerGroup.scale.set(this.innerMaterialParams.scale, this.innerMaterialParams.scale, this.innerMaterialParams.scale);
    this.innerGroup.renderOrder = 1; // 确保在地球之后渲染
    this.innerGroup.add(this.innerMesh);
    this.innerGroup.visible = this.innerMaterialParams.visible;

    // Outer mesh
    this.outerMesh = new THREE.Mesh(this.outerGeometry, this.outerMaterial);
    this.outerGroup = new THREE.Group();
    this.outerGroup.scale.set(this.outerMaterialParams.scale, this.outerMaterialParams.scale, this.outerMaterialParams.scale);
    this.outerGroup.renderOrder = 2; // 确保在inner之后渲染
    this.outerGroup.add(this.outerMesh);
    this.outerGroup.visible = this.outerMaterialParams.visible;
  }

  addToScene() {
    if (this.scene) {
      this.scene.add(this.innerGroup);
      this.scene.add(this.outerGroup);
    }
  }

  // Helper methods for converting string constants to THREE.js constants
  getSideConstant(sideString) {
    switch (sideString) {
      case "FrontSide":
        return THREE.FrontSide;
      case "BackSide":
        return THREE.BackSide;
      case "DoubleSide":
        return THREE.DoubleSide;
      default:
        return THREE.BackSide;
    }
  }

  getBlendingConstant(blendingString) {
    switch (blendingString) {
      case "NormalBlending":
        return THREE.NormalBlending;
      case "AdditiveBlending":
        return THREE.AdditiveBlending;
      case "SubtractiveBlending":
        return THREE.SubtractiveBlending;
      case "MultiplyBlending":
        return THREE.MultiplyBlending;
      default:
        return THREE.NormalBlending;
    }
  }

  // Initialize GUI controls
  initGUI() {
    // Create GUI if it doesn't exist
    if (!window.haloGUI) {
      this.gui = new GUI({
        title: "🌟 光晕控制",
        width: 320,
      });
      window.haloGUI = this.gui;
    } else {
      this.gui = window.haloGUI;
    }

    // Create halo folder
    this.folder = this.gui.addFolder("光晕设置");
    this.folder.open();

    this.setupGUIControls();
  }

  // Setup GUI controls
  setupGUIControls() {
    // Outer Material Controls
    const outerFolder = this.folder.addFolder("🔵 外层光晕 (Outer Material)");
    outerFolder.open();

    // Visibility toggle
    outerFolder
      .add(this.outerMaterialParams, "visible")
      .name("显示/隐藏")
      .onChange((value) => {
        this.outerGroup.visible = value;
      });

    // Color picker
    outerFolder
      .addColor(this.outerMaterialParams, "color")
      .name("颜色")
      .onChange((value) => {
        this.outerMaterial.uniforms.glowColor.value.set(value);
      });

    // Coeficient (glow intensity base)
    outerFolder
      .add(this.outerMaterialParams, "coeficient", -1.0, 2.0, 0.01)
      .name("发光系数")
      .onChange((value) => {
        this.outerMaterial.uniforms.coeficient.value = value;
      });

    // Power (glow falloff)
    outerFolder
      .add(this.outerMaterialParams, "power", 0.1, 10.0, 0.1)
      .name("发光强度")
      .onChange((value) => {
        this.outerMaterial.uniforms.power.value = value;
      });

    // Opacity (alpha multiplier in shader)
    outerFolder
      .add(this.outerMaterialParams, "opacity", 0.0, 1.0, 0.01)
      .name("透明度")
      .onChange((value) => {
        // Update the fragment shader's opacity uniform
        this.outerMaterial.uniforms.uOpacity.value = value;
      });

    // Scale
    outerFolder
      .add(this.outerMaterialParams, "scale", 0.5, 3.0, 0.01)
      .name("缩放")
      .onChange((value) => {
        this.outerGroup.scale.set(value, value, value);
      });

    // Geometry radius
    outerFolder
      .add(this.outerMaterialParams, "geometryRadius", 1.0, 2.0, 0.01)
      .name("几何半径")
      .onChange((value) => {
        this.recreateOuterGeometry();
      });

    // Geometry detail
    outerFolder
      .add(this.outerMaterialParams, "geometryDetail", 1, 128, 1)
      .name("几何细节")
      .onChange((value) => {
        this.recreateOuterGeometry();
      });

    // Side rendering
    outerFolder
      .add(this.outerMaterialParams, "side", this.sideOptions)
      .name("渲染面")
      .onChange((value) => {
        this.outerMaterial.side = this.getSideConstant(value);
        this.outerMaterial.needsUpdate = true;
      });

    // Depth write
    outerFolder
      .add(this.outerMaterialParams, "depthWrite")
      .name("深度写入")
      .onChange((value) => {
        this.outerMaterial.depthWrite = value;
        this.outerMaterial.needsUpdate = true;
      });

    // Depth test
    outerFolder
      .add(this.outerMaterialParams, "depthTest")
      .name("深度测试")
      .onChange((value) => {
        this.outerMaterial.depthTest = value;
        this.outerMaterial.needsUpdate = true;
      });

    // Blending mode
    outerFolder
      .add(this.outerMaterialParams, "blending", this.blendingOptions)
      .name("混合模式")
      .onChange((value) => {
        this.outerMaterial.blending = this.getBlendingConstant(value);
        this.outerMaterial.needsUpdate = true;
      });

    // Inner Material Controls (for comparison)
    const innerFolder = this.folder.addFolder("🔴 内层光晕 (Inner Material)");

    // Visibility toggle
    innerFolder
      .add(this.innerMaterialParams, "visible")
      .name("显示/隐藏")
      .onChange((value) => {
        this.innerGroup.visible = value;
      });

    // Color picker
    innerFolder
      .addColor(this.innerMaterialParams, "color")
      .name("颜色")
      .onChange((value) => {
        this.innerMaterial.uniforms.glowColor.value.set(value);
      });

    // Coeficient
    innerFolder
      .add(this.innerMaterialParams, "coeficient", -1.0, 2.0, 0.01)
      .name("发光系数")
      .onChange((value) => {
        this.innerMaterial.uniforms.coeficient.value = value;
      });

    // Power
    innerFolder
      .add(this.innerMaterialParams, "power", 0.1, 10.0, 0.1)
      .name("发光强度")
      .onChange((value) => {
        this.innerMaterial.uniforms.power.value = value;
      });

    // Opacity (alpha multiplier in shader)
    innerFolder
      .add(this.innerMaterialParams, "opacity", 0.0, 1.0, 0.01)
      .name("透明度")
      .onChange((value) => {
        // Update the fragment shader's opacity uniform
        this.innerMaterial.uniforms.uOpacity.value = value;
      });

    // Scale
    innerFolder
      .add(this.innerMaterialParams, "scale", 0.5, 3.0, 0.01)
      .name("缩放")
      .onChange((value) => {
        this.innerGroup.scale.set(value, value, value);
      });

    // Geometry radius
    innerFolder
      .add(this.innerMaterialParams, "geometryRadius", 1.0, 2.0, 0.01)
      .name("几何半径")
      .onChange(() => {
        this.recreateInnerGeometry();
      });
  }

  // Helper method to update shader opacity
  updateShaderOpacity(material, opacity) {
    // Since we can't easily modify the fragment shader at runtime,
    // we'll use the material's opacity property
    material.opacity = opacity;
    material.needsUpdate = true;
  }

  // Recreate outer geometry with new parameters
  recreateOuterGeometry() {
    // Dispose old geometry
    if (this.outerGeometry) {
      this.outerGeometry.dispose();
    }

    // Create new geometry
    this.outerGeometry = new THREE.IcosahedronGeometry(EARTH_RADIUS * this.outerMaterialParams.geometryRadius, this.outerMaterialParams.geometryDetail);

    // Update mesh
    this.outerMesh.geometry = this.outerGeometry;
  }

  // Recreate inner geometry with new parameters
  recreateInnerGeometry() {
    // Dispose old geometry
    if (this.innerGeometry) {
      this.innerGeometry.dispose();
    }

    // Create new geometry
    this.innerGeometry = new THREE.IcosahedronGeometry(EARTH_RADIUS * this.innerMaterialParams.geometryRadius, this.innerMaterialParams.geometryDetail || EARTH_FRAGMENTS);

    // Update mesh
    this.innerMesh.geometry = this.innerGeometry;
  }

  destroy() {
    // Remove GUI folder
    if (this.folder && this.gui) {
      this.gui.removeFolder(this.folder);
    }

    // Clean up GUI if no other folders exist
    if (this.gui && this.gui.folders.length === 0) {
      this.gui.destroy();
      window.haloGUI = null;
    }

    // Remove from scene
    if (this.scene) {
      if (this.innerGroup) {
        this.scene.remove(this.innerGroup);
      }
      if (this.outerGroup) {
        this.scene.remove(this.outerGroup);
      }
    }

    // Dispose of geometries
    if (this.innerGeometry) {
      this.innerGeometry.dispose();
    }
    if (this.outerGeometry) {
      this.outerGeometry.dispose();
    }

    // Dispose of materials
    if (this.innerMaterial) {
      this.innerMaterial.dispose();
    }
    if (this.outerMaterial) {
      this.outerMaterial.dispose();
    }

    // Clear references
    this.innerMesh = null;
    this.outerMesh = null;
    this.innerGeometry = null;
    this.outerGeometry = null;
    this.innerMaterial = null;
    this.outerMaterial = null;
    this.innerGroup = null;
    this.outerGroup = null;
    this.gui = null;
    this.folder = null;
  }
}

// Export both named and default exports for flexibility
export { Halo };
export default Halo;
