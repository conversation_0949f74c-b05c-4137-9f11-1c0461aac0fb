# 蜂窝网格扫光频率控制功能

## 功能概述

为HoneycombGrid组件添加了扫光频率控制参数，允许用户调整扫光效果的重复频率，提供更灵活的视觉效果控制。

## 新增参数

### `sweepFrequency`
- **类型**: `Number`
- **默认值**: `1.0`
- **范围**: `0.1 - 3.0`
- **说明**: 控制扫光效果的频率（扫光周期的倒数）
  - 值越大，扫光越频繁
  - 值越小，扫光越缓慢
  - `1.0` 表示标准频率（4秒一个完整周期）
  - `2.0` 表示双倍频率（2秒一个完整周期）
  - `0.5` 表示半倍频率（8秒一个完整周期）

## 实现细节

### 1. 参数定义
```javascript
// 在构造函数中添加
this.sweepFrequency = 1.0; // 扫光频率 (扫光周期的倒数，值越大扫光越频繁)
```

### 2. Shader Uniform
```javascript
// 在uniforms中添加
uSweepFrequency: { value: this.sweepFrequency }
```

### 3. Shader计算
```glsl
// 在fragment shader中修改周期计算
float cycleDuration = 4.0 / uSweepFrequency; // 频率越高，周期越短
float sweepCycle = mod(time * speed, cycleDuration);
float sweepPos = 1.0 - (sweepCycle / cycleDuration) * 2.0;
```

### 4. GUI控制
```javascript
this.folder
  .add(this, "sweepFrequency", 0.1, 3.0, 0.1)
  .name("扫光频率")
  .onChange((value) => {
    this.setSweepFrequency(value);
  });
```

## 新增方法

### `setSweepFrequency(frequency)`
设置扫光频率
- **参数**: `frequency` - 频率值 (0.1 - 3.0)
- **用法**: 
```javascript
honeycombGrid.setSweepFrequency(2.0); // 设置为双倍频率
```

## 使用示例

### 基本用法
```javascript
// 创建蜂窝网格实例
const honeycombGrid = new HoneycombGrid(scene, {
  isActive: true,
  opacity: 0.2,
  color: "#00ffff"
});

// 设置不同的扫光频率
honeycombGrid.setSweepFrequency(0.5); // 慢速扫光
honeycombGrid.setSweepFrequency(1.0); // 标准速度
honeycombGrid.setSweepFrequency(2.0); // 快速扫光
```

### 动态调整
```javascript
// 动态调整扫光频率
let frequency = 1.0;
setInterval(() => {
  frequency = 0.5 + Math.sin(Date.now() * 0.001) * 0.5; // 0.5-1.5之间变化
  honeycombGrid.setSweepFrequency(frequency);
}, 100);
```

## 频率效果对比

| 频率值 | 周期时长 | 效果描述 |
|--------|----------|----------|
| 0.1    | 40秒     | 极慢扫光，适合背景效果 |
| 0.5    | 8秒      | 慢速扫光，优雅平缓 |
| 1.0    | 4秒      | 标准扫光，平衡效果 |
| 1.5    | 2.67秒   | 较快扫光，活跃感强 |
| 2.0    | 2秒      | 快速扫光，动感十足 |
| 3.0    | 1.33秒   | 极快扫光，高频闪烁 |

## 与其他参数的配合

### 推荐组合
1. **慢速优雅效果**:
   - `sweepFrequency: 0.5`
   - `sweepSpeed: 0.3`
   - `sweepIntensity: 0.2`

2. **标准动态效果**:
   - `sweepFrequency: 1.0`
   - `sweepSpeed: 0.42`
   - `sweepIntensity: 0.33`

3. **快速激烈效果**:
   - `sweepFrequency: 2.0`
   - `sweepSpeed: 0.6`
   - `sweepIntensity: 0.5`

## 注意事项

1. **性能考虑**: 过高的频率值可能会增加GPU计算负担
2. **视觉效果**: 频率过高可能导致闪烁效果，建议根据实际需求调整
3. **兼容性**: 该功能与现有的所有扫光参数完全兼容
4. **默认值**: 默认频率为1.0，保持向后兼容性

## 技术实现

扫光频率通过修改shader中的周期计算实现：
- 原始固定周期：4秒
- 新的动态周期：`4.0 / sweepFrequency` 秒
- 这样确保了频率参数的直观性：值越大，扫光越频繁
