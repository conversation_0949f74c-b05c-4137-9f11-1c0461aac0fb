import * as THREE from "three";
import { G<PERSON> } from "lil-gui";
import { EARTH_FRAGMENTS, EARTH_RADIUS, PATHS } from "../../../../constants/index.js";
import { texturePreloader } from "../../utils/TexturePreloader.js";

class EarthDay {
  constructor(scene, { onLoad, renderer, enableGUI = true } = {}) {
    this.scene = scene;
    this.onLoad = onLoad;
    this.renderer = renderer;
    this.enableGUI = enableGUI;
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {
      day: null,
      bump: null,
      specular: null,
    };

    // GUI 相关属性
    this.gui = null;
    this.folder = null;

    // 纹理上传相关属性
    this.originalTextures = {
      day: null,
      bump: null,
      specular: null,
    }; // 保存原始纹理的引用
    this.customTextures = {
      day: null,
      bump: null,
      specular: null,
    }; // 用户上传的自定义纹理

    // Material parameters for GUI control (MeshPhongMaterial)
    this.materialParams = {
      // 基础属性
      color: "#ffffff",
      opacity: 1.0,
      transparent: true,

      // Phong材质特有属性
      emissive: "#000000",
      emissiveIntensity: 0.0,
      specular: "#111111",
      shininess: 30,

      // 凹凸贴图
      bumpScale: 12.4,

      // 反射率
      reflectivity: 1.0,
      refractionRatio: 0.98,

      // 线框模式
      wireframe: false,
      wireframeLinewidth: 1,
    };

    this.init();
  }

  async init() {
    await this.loadTextures();
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.addToScene();

    // Initialize GUI controls
    if (this.enableGUI) {
      // this.initGUI();
    }

    // Call onLoad callback if provided
    if (this.onLoad) {
      this.onLoad();
    }
  }

  async loadTextures() {
    try {
      // 优先使用预加载的纹理
      const preloadedTextures = texturePreloader.getAllTextures();

      if (preloadedTextures.earthMap && preloadedTextures.bumpMap && preloadedTextures.specularMap) {
        console.log("EarthDay: 使用预加载的纹理");

        this.textures.day = preloadedTextures.earthMap;
        this.textures.bump = preloadedTextures.bumpMap;
        this.textures.specular = preloadedTextures.specularMap;

        // 保存原始纹理的引用
        this.originalTextures.day = preloadedTextures.earthMap;
        this.originalTextures.bump = preloadedTextures.bumpMap;
        this.originalTextures.specular = preloadedTextures.specularMap;
      } else {
        console.log("EarthDay: 预加载纹理不可用，使用异步加载");

        // 如果预加载纹理不可用，使用传统的异步加载方式
        const textureLoader = new THREE.TextureLoader();
        const [dayTexture, bumpTexture, specularTexture] = await Promise.all([
          this.loadTexture(textureLoader, PATHS.earthMap),
          this.loadTexture(textureLoader, PATHS.bumpMap),
          this.loadTexture(textureLoader, PATHS.specularMap),
        ]);

        this.textures.day = dayTexture;
        this.textures.bump = bumpTexture;
        this.textures.specular = specularTexture;

        // 保存原始纹理的引用
        this.originalTextures.day = dayTexture;
        this.originalTextures.bump = bumpTexture;
        this.originalTextures.specular = specularTexture;
      }

      // Set color space for proper color rendering
      this.textures.day.colorSpace = THREE.SRGBColorSpace; // Color texture needs sRGB
      this.textures.bump.colorSpace = THREE.NoColorSpace; // Data texture, no color space
      this.textures.specular.colorSpace = THREE.NoColorSpace; // Data texture, no color space

      // Set anisotropy for better quality - get actual max from renderer
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      this.textures.day.anisotropy = maxAnisotropy;
      this.textures.bump.anisotropy = maxAnisotropy;
      this.textures.specular.anisotropy = maxAnisotropy;

      console.log("EarthDay: 纹理加载完成");
    } catch (error) {
      console.error("Error loading EarthDay textures:", error);
    }
  }

  loadTexture(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  createGeometry() {
    this.geometry = new THREE.SphereGeometry(EARTH_RADIUS, EARTH_FRAGMENTS, EARTH_FRAGMENTS);
  }

  createMaterial() {
    this.material = new THREE.MeshPhongMaterial({
      // 贴图
      map: this.textures.day,
      bumpMap: this.textures.bump,
      specularMap: this.textures.specular,

      // 基础属性
      color: new THREE.Color(this.materialParams.color),
      opacity: this.materialParams.opacity,
      transparent: this.materialParams.transparent,

      // Phong材质特有属性
      emissive: new THREE.Color(this.materialParams.emissive),
      emissiveIntensity: this.materialParams.emissiveIntensity,
      specular: new THREE.Color(this.materialParams.specular),
      shininess: this.materialParams.shininess,

      // 凹凸贴图
      bumpScale: this.materialParams.bumpScale,

      // 反射率
      reflectivity: this.materialParams.reflectivity,
      refractionRatio: this.materialParams.refractionRatio,

      // 线框模式
      wireframe: this.materialParams.wireframe,
      wireframeLinewidth: this.materialParams.wireframeLinewidth,
    });
  }

  createMesh() {
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    this.mesh.castShadow = true;
    this.mesh.scale.set(0.999, 0.999, 0.999);
    this.mesh.rotation.y = Math.PI;
    this.mesh.renderOrder = 0; // 地球作为基础层

    const sphere = new THREE.Mesh(
      new THREE.SphereGeometry(EARTH_RADIUS, EARTH_FRAGMENTS, EARTH_FRAGMENTS),
      new THREE.MeshPhysicalMaterial({ color: 0xffffff, transparent: true, opacity: 0.01 })
    );
    sphere.scale.set(1, 1, 1);
    this.scene.add(sphere);
  }

  addToScene() {
    if (this.scene && this.mesh) {
      // this.mesh.visible = false;
      this.scene.add(this.mesh);
    }
  }

  /**
   * 上传并替换指定类型的纹理
   * @param {File} file - 用户上传的图片文件
   * @param {string} textureType - 纹理类型 ('day', 'bump', 'specular')
   * @returns {Promise<boolean>} 是否成功替换纹理
   */
  async uploadTexture(file, textureType) {
    try {
      // 验证文件类型
      if (!file || !file.type.startsWith("image/")) {
        throw new Error("请选择有效的图片文件");
      }

      // 验证纹理类型
      if (!["day", "bump", "specular"].includes(textureType)) {
        throw new Error("无效的纹理类型");
      }

      // 创建文件URL
      const imageUrl = URL.createObjectURL(file);

      // 加载新纹理
      const textureLoader = new THREE.TextureLoader();
      const newTexture = await this.loadTextureFromUrl(textureLoader, imageUrl);

      // 设置纹理属性
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      newTexture.anisotropy = maxAnisotropy;
      newTexture.wrapS = THREE.RepeatWrapping;
      newTexture.wrapT = THREE.RepeatWrapping;

      // 设置颜色空间
      if (textureType === "day") {
        newTexture.colorSpace = THREE.SRGBColorSpace; // 颜色纹理需要sRGB
      } else {
        newTexture.colorSpace = THREE.NoColorSpace; // 数据纹理，无颜色空间
      }

      // 释放之前的自定义纹理（如果存在）
      if (this.customTextures[textureType]) {
        this.customTextures[textureType].dispose();
      }

      // 保存新的自定义纹理
      this.customTextures[textureType] = newTexture;

      // 更新材质中的纹理
      this.textures[textureType] = newTexture;
      this.updateMaterialTexture(textureType, newTexture);

      // 清理临时URL
      URL.revokeObjectURL(imageUrl);

      console.log(`${textureType}纹理上传成功`);
      return true;
    } catch (error) {
      console.error(`${textureType}纹理上传失败:`, error);
      throw error;
    }
  }

  /**
   * 从URL加载纹理（Promise版本）
   * @param {THREE.TextureLoader} loader - 纹理加载器
   * @param {string} url - 纹理URL
   * @returns {Promise<THREE.Texture>} 加载的纹理
   */
  loadTextureFromUrl(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  /**
   * 更新材质中的指定纹理
   * @param {string} textureType - 纹理类型
   * @param {THREE.Texture} texture - 新纹理
   */
  updateMaterialTexture(textureType, texture) {
    if (!this.material) return;

    switch (textureType) {
      case "day":
        this.material.map = texture;
        break;
      case "bump":
        this.material.bumpMap = texture;
        break;
      case "specular":
        this.material.specularMap = texture;
        break;
    }

    this.material.needsUpdate = true;
  }

  /**
   * 恢复指定类型的原始纹理
   * @param {string} textureType - 纹理类型 ('day', 'bump', 'specular')
   */
  restoreOriginalTexture(textureType) {
    if (!this.originalTextures[textureType]) {
      console.warn(`没有找到原始${textureType}纹理`);
      return;
    }

    // 释放自定义纹理
    if (this.customTextures[textureType]) {
      this.customTextures[textureType].dispose();
      this.customTextures[textureType] = null;
    }

    // 恢复原始纹理
    this.textures[textureType] = this.originalTextures[textureType];
    this.updateMaterialTexture(textureType, this.originalTextures[textureType]);

    console.log(`已恢复原始${textureType}纹理`);
  }

  /**
   * 恢复所有原始纹理
   */
  restoreAllOriginalTextures() {
    ["day", "bump", "specular"].forEach((textureType) => {
      this.restoreOriginalTexture(textureType);
    });
  }

  /**
   * 初始化GUI控制面板
   */
  initGUI() {
    try {
      // 获取或创建全局GUI实例
      if (window.gui) {
        this.gui = window.gui;
      } else {
        this.gui = new GUI();
        window.gui = this.gui;
      }

      // 创建地球材质控制文件夹
      this.folder = this.gui.addFolder("🌍 地球材质 (Earth Material)");
      this.folder.open();

      this.setupGUIControls();
    } catch (error) {
      console.warn("EarthDay GUI setup warning:", error);
    }
  }

  /**
   * 设置GUI控制项
   */
  setupGUIControls() {
    if (!this.folder || !this.material) return;

    // 基础属性文件夹
    const basicFolder = this.folder.addFolder("🎨 基础属性 (Basic Properties)");
    basicFolder.open();

    // 颜色控制
    basicFolder
      .addColor(this.materialParams, "color")
      .name("颜色 (Color)")
      .onChange((value) => {
        this.material.color.set(value);
      });

    // 透明度控制
    basicFolder
      .add(this.materialParams, "opacity", 0, 1, 0.01)
      .name("透明度 (Opacity)")
      .onChange((value) => {
        this.material.opacity = value;
      });

    // 透明开关
    basicFolder
      .add(this.materialParams, "transparent")
      .name("透明 (Transparent)")
      .onChange((value) => {
        this.material.transparent = value;
      });

    // Phong材质属性文件夹
    const phongFolder = this.folder.addFolder("💡 Phong材质属性 (Phong Properties)");
    phongFolder.open();

    // 自发光颜色
    phongFolder
      .addColor(this.materialParams, "emissive")
      .name("自发光颜色 (Emissive)")
      .onChange((value) => {
        this.material.emissive.set(value);
      });

    // 自发光强度
    phongFolder
      .add(this.materialParams, "emissiveIntensity", 0, 2, 0.01)
      .name("自发光强度 (Emissive Intensity)")
      .onChange((value) => {
        this.material.emissiveIntensity = value;
      });

    // 镜面反射颜色
    phongFolder
      .addColor(this.materialParams, "specular")
      .name("镜面反射颜色 (Specular)")
      .onChange((value) => {
        this.material.specular.set(value);
      });

    // 光泽度
    phongFolder
      .add(this.materialParams, "shininess", 0, 100, 1)
      .name("光泽度 (Shininess)")
      .onChange((value) => {
        this.material.shininess = value;
      });

    // 凹凸贴图强度
    phongFolder
      .add(this.materialParams, "bumpScale", 0, 50, 0.1)
      .name("凹凸强度 (Bump Scale)")
      .onChange((value) => {
        this.material.bumpScale = value;
      });

    // 反射率
    phongFolder
      .add(this.materialParams, "reflectivity", 0, 1, 0.01)
      .name("反射率 (Reflectivity)")
      .onChange((value) => {
        this.material.reflectivity = value;
      });

    // 折射比
    phongFolder
      .add(this.materialParams, "refractionRatio", 0, 1, 0.01)
      .name("折射比 (Refraction Ratio)")
      .onChange((value) => {
        this.material.refractionRatio = value;
      });

    // 渲染模式文件夹
    const renderFolder = this.folder.addFolder("🔧 渲染模式 (Render Mode)");

    // 线框模式
    renderFolder
      .add(this.materialParams, "wireframe")
      .name("线框模式 (Wireframe)")
      .onChange((value) => {
        this.material.wireframe = value;
      });

    // 线框线宽
    renderFolder
      .add(this.materialParams, "wireframeLinewidth", 1, 10, 1)
      .name("线框线宽 (Wireframe Linewidth)")
      .onChange((value) => {
        this.material.wireframeLinewidth = value;
      });

    // 预设按钮文件夹
    const presetsFolder = this.folder.addFolder("🎯 预设 (Presets)");
    presetsFolder.open();

    const presets = {
      resetToDefault: () => this.resetToDefault(),
      earthLike: () => this.applyEarthPreset(),
      metallic: () => this.applyMetallicPreset(),
      bright: () => this.applyBrightPreset(),
      dark: () => this.applyDarkPreset(),
    };

    presetsFolder.add(presets, "resetToDefault").name("🔄 重置默认");
    presetsFolder.add(presets, "earthLike").name("🌍 地球风格");
    presetsFolder.add(presets, "metallic").name("⚡ 金属风格");
    presetsFolder.add(presets, "bright").name("☀️ 明亮风格");
    presetsFolder.add(presets, "dark").name("🌙 暗色风格");

    // 添加纹理上传控件
    this.setupTextureUploadControls();
  }

  /**
   * 设置纹理上传控件
   */
  setupTextureUploadControls() {
    if (!this.folder) return;

    // 创建纹理上传子文件夹
    const uploadFolder = this.folder.addFolder("📁 纹理上传 (Texture Upload)");
    uploadFolder.open();

    // 创建隐藏的文件输入元素
    this.fileInputs = {};
    ["day", "bump", "specular"].forEach((textureType) => {
      const fileInput = document.createElement("input");
      fileInput.type = "file";
      fileInput.accept = "image/*";
      fileInput.style.display = "none";
      document.body.appendChild(fileInput);
      this.fileInputs[textureType] = fileInput;
    });

    // 白天纹理上传控件
    const dayFolder = uploadFolder.addFolder("☀️ 白天纹理 (Day Texture)");
    this.setupSingleTextureUpload(dayFolder, "day", "白天纹理");

    // 凹凸纹理上传控件
    const bumpFolder = uploadFolder.addFolder("🏔️ 凹凸纹理 (Bump Map)");
    this.setupSingleTextureUpload(bumpFolder, "bump", "凹凸纹理");

    // 镜面纹理上传控件
    const specularFolder = uploadFolder.addFolder("💎 镜面纹理 (Specular Map)");
    this.setupSingleTextureUpload(specularFolder, "specular", "镜面纹理");

    // 全局控制
    const globalControls = {
      restoreAllOriginal: () => {
        this.restoreAllOriginalTextures();
      },
    };

    uploadFolder.add(globalControls, "restoreAllOriginal").name("🔄 恢复所有原始纹理");

    // 添加使用说明
    const infoFolder = uploadFolder.addFolder("ℹ️ 使用说明");
    const info = {
      info1: "白天纹理：地球表面的颜色贴图",
      info2: "凹凸纹理：表面高度信息（黑白图）",
      info3: "镜面纹理：反射强度信息（黑白图）",
      info4: "建议使用 2048x1024 或更高分辨率",
      info5: "支持 JPG, PNG, WebP 等图片格式",
    };

    // 由于lil-gui不直接支持只读文本，我们使用按钮来显示信息
    Object.entries(info).forEach(([key, text]) => {
      const infoControl = {};
      infoControl[key] = () => {
        console.log(text);
      };
      infoFolder.add(infoControl, key).name(text).disable();
    });
  }

  /**
   * 设置单个纹理类型的上传控件
   * @param {Object} folder - GUI文件夹
   * @param {string} textureType - 纹理类型
   * @param {string} displayName - 显示名称
   */
  setupSingleTextureUpload(folder, textureType, displayName) {
    const fileInput = this.fileInputs[textureType];

    // 上传按钮控件
    const uploadControls = {
      upload: () => {
        fileInput.click();
      },
      restore: () => {
        this.restoreOriginalTexture(textureType);
      },
    };

    // 文件选择事件处理
    fileInput.addEventListener("change", async (event) => {
      const file = event.target.files[0];
      if (file) {
        try {
          console.log(`开始上传${displayName}...`);

          await this.uploadTexture(file, textureType);

          console.log(`${displayName}上传完成！`);
          this.showUploadSuccess(displayName);
        } catch (error) {
          console.error(`${displayName}上传失败:`, error);
          this.showUploadError(displayName, error.message);
        }

        // 清空文件输入，允许重复选择同一文件
        fileInput.value = "";
      }
    });

    // 添加上传按钮
    folder.add(uploadControls, "upload").name(`📤 上传${displayName}`);

    // 添加恢复原始纹理按钮
    folder.add(uploadControls, "restore").name(`🔄 恢复原始${displayName}`);
  }

  /**
   * 显示上传成功提示
   * @param {string} textureType - 纹理类型
   */
  showUploadSuccess(textureType) {
    console.log(`✅ ${textureType}上传成功！`);

    if (typeof window !== "undefined" && window.alert) {
      setTimeout(() => {
        console.log(`${textureType}已成功替换`);
      }, 100);
    }
  }

  /**
   * 显示上传错误提示
   * @param {string} textureType - 纹理类型
   * @param {string} message - 错误消息
   */
  showUploadError(textureType, message) {
    console.error(`❌ ${textureType}上传失败:`, message);

    if (typeof window !== "undefined" && window.alert) {
      setTimeout(() => {
        console.error(`${textureType}上传失败:`, message);
      }, 100);
    }
  }

  /**
   * 重置为默认值
   */
  resetToDefault() {
    this.materialParams = {
      color: "#ffffff",
      opacity: 1.0,
      transparent: true,
      emissive: "#000000",
      emissiveIntensity: 0.0,
      specular: "#111111",
      shininess: 30,
      bumpScale: 12.4,
      reflectivity: 1.0,
      refractionRatio: 0.98,
      wireframe: false,
      wireframeLinewidth: 1,
    };
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用地球风格预设
   */
  applyEarthPreset() {
    Object.assign(this.materialParams, {
      color: "#ffffff",
      specular: "#222222",
      shininess: 25,
      bumpScale: 15.75,
      emissive: "#000000",
      emissiveIntensity: 0.0,
      reflectivity: 0.8,
      wireframe: false,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用金属风格预设
   */
  applyMetallicPreset() {
    Object.assign(this.materialParams, {
      color: "#cccccc",
      specular: "#ffffff",
      shininess: 80,
      bumpScale: 8.0,
      emissive: "#000000",
      emissiveIntensity: 0.0,
      reflectivity: 1.0,
      wireframe: false,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用明亮风格预设
   */
  applyBrightPreset() {
    Object.assign(this.materialParams, {
      color: "#ffffff",
      specular: "#ffffff",
      shininess: 60,
      bumpScale: 10.0,
      emissive: "#001122",
      emissiveIntensity: 0.3,
      reflectivity: 0.9,
      wireframe: false,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 应用暗色风格预设
   */
  applyDarkPreset() {
    Object.assign(this.materialParams, {
      color: "#888888",
      specular: "#444444",
      shininess: 15,
      bumpScale: 20.0,
      emissive: "#000000",
      emissiveIntensity: 0.0,
      reflectivity: 0.4,
      wireframe: false,
    });
    this.updateMaterialFromParams();
    this.updateGUIFromParams();
  }

  /**
   * 从参数更新材质
   */
  updateMaterialFromParams() {
    if (!this.material) return;

    this.material.color.set(this.materialParams.color);
    this.material.opacity = this.materialParams.opacity;
    this.material.transparent = this.materialParams.transparent;
    this.material.emissive.set(this.materialParams.emissive);
    this.material.emissiveIntensity = this.materialParams.emissiveIntensity;
    this.material.specular.set(this.materialParams.specular);
    this.material.shininess = this.materialParams.shininess;
    this.material.bumpScale = this.materialParams.bumpScale;
    this.material.reflectivity = this.materialParams.reflectivity;
    this.material.refractionRatio = this.materialParams.refractionRatio;
    this.material.wireframe = this.materialParams.wireframe;
    this.material.wireframeLinewidth = this.materialParams.wireframeLinewidth;

    this.material.needsUpdate = true;
  }

  /**
   * 从参数更新GUI显示
   */
  updateGUIFromParams() {
    if (!this.folder) return;

    // 遍历所有控制器并更新显示值
    this.folder.controllersRecursive().forEach((controller) => {
      controller.updateDisplay();
    });
  }

  destroy() {
    // 清理文件输入元素
    if (this.fileInputs) {
      Object.values(this.fileInputs).forEach((fileInput) => {
        if (fileInput && fileInput.parentNode) {
          fileInput.parentNode.removeChild(fileInput);
        }
      });
      this.fileInputs = null;
    }

    // Remove from scene
    if (this.scene && this.mesh) {
      this.scene.remove(this.mesh);
    }

    // Dispose of geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Dispose of material
    if (this.material) {
      this.material.dispose();
    }

    // Dispose of textures
    Object.values(this.textures).forEach((texture) => {
      if (texture) {
        texture.dispose();
      }
    });

    // 清理自定义纹理
    if (this.customTextures) {
      Object.values(this.customTextures).forEach((texture) => {
        if (texture) {
          texture.dispose();
        }
      });
      this.customTextures = {};
    }

    // Clean up GUI
    if (this.folder) {
      this.folder.destroy();
      this.folder = null;
    }

    // Clear references
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.textures = {};
    this.originalTextures = {};
    this.gui = null;
  }
}

// Export both named and default exports for flexibility
export { EarthDay };
export default EarthDay;
