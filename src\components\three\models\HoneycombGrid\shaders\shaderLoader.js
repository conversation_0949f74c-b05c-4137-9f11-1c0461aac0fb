// Shader loader utility for HoneycombGrid
// Loads and provides vertex and fragment shaders

// Inline vertex shader
export const vertexShader = `
// Vertex shader for HoneycombGrid with sweeping light effect
// Transforms vertices and passes necessary data to fragment shader

// Uniforms
uniform float uTime;
uniform vec3 uSweepDirection;
uniform float uSweepSpeed;

// Varyings to pass to fragment shader
varying vec3 vWorldPosition;
varying vec3 vLocalPosition;
varying vec2 vUv;
varying float vDistanceFromCenter;

void main() {
    // Transform position to world coordinates
    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
    vWorldPosition = worldPosition.xyz;

    // Store local position for calculations
    vLocalPosition = position;

    // Calculate UV coordinates based on spherical projection
    // This helps with sweep calculations
    vec3 normalized = normalize(position);
    vUv.x = atan(normalized.z, normalized.x) / (2.0 * 3.14159265) + 0.5;
    vUv.y = asin(normalized.y) / 3.14159265 + 0.5;

    // Calculate distance from center (useful for radial sweeps)
    vDistanceFromCenter = length(position);

    // Standard vertex transformation
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
`;

// Inline fragment shader
export const fragmentShader = `
// Fragment shader for HoneycombGrid with sweeping light effect
// Calculates sweep lighting and applies smooth transitions

// Uniforms
uniform float uTime;
uniform vec3 uSweepDirection;
uniform float uSweepSpeed;
uniform float uSweepWidth;
uniform float uSweepIntensity;
uniform float uBaseOpacity;
uniform vec3 uColor;
uniform float uSweepFalloff;
uniform float uSweepRotation;
uniform float uIntensityDecay; // 强度衰减速度控制

// Varyings from vertex shader
varying vec3 vWorldPosition;
varying vec3 vLocalPosition;
varying vec2 vUv;
varying float vDistanceFromCenter;

// Constants
const float PI = 3.14159265359;
const float TWO_PI = 6.28318530718;

// Function to calculate linear sweep with rotation support and intensity decay
float calculateLinearSweep(vec3 position, vec3 direction, float time, float speed, float width) {
    // Calculate sphere radius
    float sphereRadius = length(position);

    // Normalize position to sphere surface
    vec3 normalizedPos = position / sphereRadius;

    // Convert rotation angle from degrees to radians
    float rotationRad = uSweepRotation * 3.14159265 / 180.0;

    // Create rotation matrix around Z axis (for rotating sweep direction)
    float cosRot = cos(rotationRad);
    float sinRot = sin(rotationRad);

    // Apply rotation to the position
    vec3 rotatedPos = vec3(
        normalizedPos.x * cosRot - normalizedPos.y * sinRot,
        normalizedPos.x * sinRot + normalizedPos.y * cosRot,
        normalizedPos.z
    );

    // Use the rotated Y coordinate for sweep calculation
    float sweepCoord = rotatedPos.y;

    // Create continuous sweeping motion from top (1) to bottom (-1)
    float sweepCycle = mod(time * speed, 4.0); // 4 second cycle
    float sweepPos = 1.0 - (sweepCycle / 4.0) * 2.0; // Continuous sweep from 1 to -1

    // Calculate distance from sweep line
    float distanceFromSweep = abs(sweepCoord - sweepPos);

    // Use uSweepFalloff to control the transition intensity
    // Higher values create sharper transitions, lower values create softer transitions
    float falloffPower = mix(0.5, 3.0, uSweepFalloff); // Map 0-1 to 0.5-3.0 for smooth to sharp
    float sweepFalloff = 1.0 - smoothstep(0.0, width * 0.5, distanceFromSweep);
    sweepFalloff = pow(sweepFalloff, falloffPower);

    // Calculate intensity decay based on sweep progress
    // sweepProgress: 0 at start (top), 1 at end (bottom)
    float sweepProgress = (1.0 - sweepPos) * 0.5; // Convert from [-1,1] to [0,1]

    // Apply intensity decay: starts at full intensity, decreases over time
    // uIntensityDecay controls how fast the intensity decreases
    float intensityMultiplier = exp(-sweepProgress * uIntensityDecay);

    // Add a trailing glow effect with falloff control and intensity decay
    float trailEffect = 0.0;
    if (sweepCoord < sweepPos) {
        float trailDistance = sweepPos - sweepCoord;
        float trailFalloff = mix(2.0, 5.0, uSweepFalloff); // Adjust trail sharpness
        trailEffect = exp(-trailDistance * trailFalloff) * (0.3 * (1.0 - uSweepFalloff * 0.5));
        // Apply intensity decay to trail effect as well
        trailEffect *= intensityMultiplier;
    }

    // Apply intensity decay to main sweep effect
    float intensity = (sweepFalloff * intensityMultiplier) + trailEffect;

    return clamp(intensity, 0.0, 1.0);
}



void main() {
    // Calculate top-to-bottom sweep intensity
    float sweepIntensity = calculateLinearSweep(vLocalPosition, uSweepDirection, uTime, uSweepSpeed, uSweepWidth);

    // Apply intensity multiplier
    sweepIntensity *= uSweepIntensity;

    // Calculate final opacity (base + sweep highlight)
    float finalOpacity = uBaseOpacity + sweepIntensity;
    finalOpacity = clamp(finalOpacity, 0.0, 1.0);

    // Set final color with calculated opacity
    gl_FragColor = vec4(uColor, finalOpacity);
}
`;

// Alternative export for easier importing
export const honeycombShaders = {
  vertex: vertexShader,
  fragment: fragmentShader,
};
