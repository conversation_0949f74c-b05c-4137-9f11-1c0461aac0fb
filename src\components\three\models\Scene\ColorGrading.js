import * as THREE from "three";
import { BrightnessContrastShader } from "three/examples/jsm/shaders/BrightnessContrastShader.js";
import { HueSaturationShader } from "three/examples/jsm/shaders/HueSaturationShader.js";
import { ShaderPass } from "three/examples/jsm/postprocessing/ShaderPass.js";
import { GUI } from "lil-gui";

export class ColorGrading {
  constructor(composer, gui = null) {
    this.composer = composer;
    this.gui = gui;
    this.folder = null;

    this.brightnessContrastPass = null;
    this.hueSaturationPass = null;
    this.params = {
      enabled: true,
      brightness: 0, // 亮度
      contrast: 0.1, // 对比度
      hue: 0.0, // 色相
      saturation: 0.05, // 饱和度
    };

    this.init();
    // this.initGui();
  }
  init() {
    // 亮度对比度通道
    this.brightnessContrastPass = new ShaderPass(BrightnessContrastShader);
    this.brightnessContrastPass.uniforms.brightness.value = this.params.brightness;
    this.brightnessContrastPass.uniforms.contrast.value = this.params.contrast;
    this.brightnessContrastPass.enabled = this.params.enabled;

    // 色相饱和度通道
    this.hueSaturationPass = new ShaderPass(HueSaturationShader);
    this.hueSaturationPass.uniforms.hue.value = this.params.hue;
    this.hueSaturationPass.uniforms.saturation.value = this.params.saturation;
    this.hueSaturationPass.enabled = this.params.enabled;

    // 添加到composer
    if (this.composer) {
      this.composer.addPass(this.brightnessContrastPass);
      this.composer.addPass(this.hueSaturationPass);
    }
  }
  initGui(parentFolder = null) {
    // 如果没有传入parentFolder，尝试使用this.gui
    if (!parentFolder) {
      parentFolder = this.gui;
    }

    if (!parentFolder) {
      console.warn("ColorGrading: No GUI or parent folder available");
      return;
    }
    // 创建颜色分级文件夹
    this.folder = parentFolder.addFolder("🎨 颜色分级 (Color Grading)");
    this.folder.open();

    // 启用/禁用开关
    this.folder
      .add(this.params, "enabled")
      .name("启用颜色分级")
      .onChange((value) => {
        this.brightnessContrastPass.enabled = value;
        this.hueSaturationPass.enabled = value;
      });

    // 亮度控制
    this.folder
      .add(this.params, "brightness", -0.5, 0.5, 0.01)
      .name("亮度")
      .onChange((value) => {
        this.brightnessContrastPass.uniforms.brightness.value = value;
      });

    // 对比度控制
    this.folder
      .add(this.params, "contrast", -0.5, 0.5, 0.01)
      .name("对比度")
      .onChange((value) => {
        this.brightnessContrastPass.uniforms.contrast.value = value;
      });

    // 色相控制
    this.folder
      .add(this.params, "hue", -Math.PI, Math.PI, 0.01)
      .name("色相")
      .onChange((value) => {
        this.hueSaturationPass.uniforms.hue.value = value;
      });

    // 饱和度控制
    this.folder
      .add(this.params, "saturation", -1, 1, 0.01)
      .name("饱和度")
      .onChange((value) => {
        this.hueSaturationPass.uniforms.saturation.value = value;
      });

    // 重置按钮
    this.folder.add({ reset: () => this.resetParams() }, "reset").name("重置颜色分级");
  }

  /**
   * 重置参数到默认值
   */
  resetParams() {
    this.params.brightness = 0;
    this.params.contrast = 0.08;
    this.params.hue = 0.0;
    this.params.saturation = 0.0;

    // 更新着色器uniform值
    this.brightnessContrastPass.uniforms.brightness.value = this.params.brightness;
    this.brightnessContrastPass.uniforms.contrast.value = this.params.contrast;
    this.hueSaturationPass.uniforms.hue.value = this.params.hue;
    this.hueSaturationPass.uniforms.saturation.value = this.params.saturation;
  }

  /**
   * 获取当前参数
   */
  getParams() {
    return { ...this.params };
  }

  /**
   * 设置参数
   */
  setParams(newParams) {
    Object.assign(this.params, newParams);

    // 更新着色器uniform值
    this.brightnessContrastPass.uniforms.brightness.value = this.params.brightness;
    this.brightnessContrastPass.uniforms.contrast.value = this.params.contrast;
    this.hueSaturationPass.uniforms.hue.value = this.params.hue;
    this.hueSaturationPass.uniforms.saturation.value = this.params.saturation;

    // 更新启用状态
    this.brightnessContrastPass.enabled = this.params.enabled;
    this.hueSaturationPass.enabled = this.params.enabled;
  }
  destroy() {
    // 清理GUI
    if (this.folder) {
      this.folder.destroy();
      this.folder = null;
    }

    // 清理引用
    this.brightnessContrastPass = null;
    this.hueSaturationPass = null;
    this.composer = null;
    this.gui = null;
  }
}
